import { Context } from 'hono';

import type {
  ImageUploadInput,
  ImageDeleteInput,
  ImageListQuery,
} from './schema';
import { createImageService } from './service';
import { getDB } from '@/infrastructure';
import { recordLog } from '@/utils/auditLog';
import { jsonError, jsonSuccess, validationError } from '@/utils/errorResponse';

/**
 * 图片上传接口
 */
export async function uploadImage(c: Context) {
  try {
    const formData = await c.req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return validationError(c, { file: '文件不能为空' });
    }

    // 获取元数据
    const metadata: ImageUploadInput = {
      category: formData.get('category') as any,
      resourceId: formData.get('resourceId') as string,
      imageType: formData.get('imageType') as any,
      variant: formData.get('variant') as any,
      groupId: (formData.get('groupId') as string) || undefined,
    };

    // 验证必填字段
    if (
      !metadata.category ||
      !metadata.resourceId ||
      !metadata.imageType ||
      !metadata.variant
    ) {
      return validationError(c, {
        category: metadata.category ? undefined : '必填字段',
        resourceId: metadata.resourceId ? undefined : '必填字段',
        imageType: metadata.imageType ? undefined : '必填字段',
        variant: metadata.variant ? undefined : '必填字段',
      });
    }

    const db = getDB(c);
    const r2 = c.env.R2;
    const imageService = createImageService(db, r2);

    const result = await imageService.uploadImage(file, metadata);

    await recordLog(c, {
      action: 'UPLOAD_IMAGE',
      targetType: 'image',
      targetId: result.id,
      meta: JSON.stringify({
        resourceType: metadata.category,
        resourceId: metadata.resourceId,
        variant: metadata.variant,
      }),
    });

    return jsonSuccess(c, '图片上传成功', result, 201);
  } catch (error: unknown) {
    if (error instanceof Error) {
      // 检查是否是验证错误
      if ((error as any).isValidationError) {
        return validationError(c, { file: error.message });
      }
      return jsonError(c, 50001, error.message, 400);
    }
    throw error;
  }
}

/**
 * 获取资源的图片列表
 */
export async function getImagesByResource(c: Context) {
  try {
    const category = c.req.param('category');
    const resourceId = c.req.param('resourceId');

    if (!category || !resourceId) {
      return jsonError(c, 40001, '参数错误', 400);
    }

    const query: ImageListQuery = {
      page: c.req.query('page'),
      pageSize: c.req.query('pageSize'),
      variant: c.req.query('variant') as any,
      imageType: c.req.query('imageType') as any,
    };

    const db = getDB(c);
    const r2 = c.env.R2;
    const imageService = createImageService(db, r2);

    const result = await imageService.getImagesByResource(
      category,
      resourceId,
      query
    );

    return c.json({
      code: 0,
      message: 'OK',
      data: {
        images: result.images,
        pagination: {
          page: parseInt(query.page || '1'),
          pageSize: parseInt(query.pageSize || '20'),
          total: result.total,
          totalPages: Math.ceil(
            result.total / parseInt(query.pageSize || '20')
          ),
        },
      },
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      return jsonError(c, 50002, error.message, 500);
    }
    throw error;
  }
}

/**
 * 删除图片
 */
export async function deleteImages(c: Context) {
  try {
    const body: ImageDeleteInput = await c.req.json();

    if (!body.relativePaths || body.relativePaths.length === 0) {
      return validationError(c, { relativePaths: '必须提供要删除的图片路径' });
    }

    const db = getDB(c);
    const r2 = c.env.R2;
    const imageService = createImageService(db, r2);

    const result = await imageService.deleteImages(body.relativePaths);

    await recordLog(c, {
      action: 'DELETE_IMAGES',
      targetType: 'image',
      meta: JSON.stringify({
        deletedCount: result.deletedCount,
        failedPaths: result.failedPaths,
      }),
    });

    return jsonSuccess(c, '图片删除完成', result);
  } catch (error: unknown) {
    if (error instanceof Error) {
      return jsonError(c, 50003, error.message, 500);
    }
    throw error;
  }
}

/**
 * 获取单个图片信息
 */
export async function getImageById(c: Context) {
  try {
    const id = c.req.param('id');

    if (!id) {
      return jsonError(c, 40001, '图片ID不能为空', 400);
    }

    const db = getDB(c);
    const { createImageRepository } = await import('./repository');
    const repo = createImageRepository(db);
    const image = await repo.findById(id);

    if (!image) {
      return jsonError(c, 40404, '图片不存在', 404);
    }

    return jsonSuccess(c, '获取图片信息成功', image);
  } catch (error: unknown) {
    if (error instanceof Error) {
      return jsonError(c, 50004, error.message, 500);
    }
    throw error;
  }
}

/**
 * 直接访问图片文件（代理 R2 桶中的图片）
 */
export async function serveImage(c: Context) {
  try {
    const id = c.req.param('id');

    if (!id) {
      return jsonError(c, 40001, '图片ID不能为空', 400);
    }

    const db = getDB(c);
    const { createImageRepository } = await import('./repository');
    const repo = createImageRepository(db);
    const image = await repo.findById(id);

    if (!image) {
      return jsonError(c, 40404, '图片不存在', 404);
    }

    // 从 R2 桶获取图片内容
    const filePath = image.file_path;
    if (!filePath) {
      return jsonError(c, 40404, '图片路径不存在', 404);
    }

    const key = filePath.startsWith('/') ? filePath.slice(1) : filePath;
    const object = await c.env.R2.get(key);

    if (!object) {
      return jsonError(c, 40404, '图片文件不存在', 404);
    }

    // 设置适当的响应头
    const headers = new Headers();
    headers.set(
      'Content-Type',
      object.httpMetadata?.contentType || `image/${image.format}`
    );
    headers.set('Content-Length', object.size.toString());
    headers.set('Cache-Control', 'public, max-age=31536000'); // 缓存1年
    headers.set('ETag', object.httpEtag || '');

    // 返回图片流
    return new Response(object.body, {
      headers,
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      return jsonError(c, 50004, error.message, 500);
    }
    throw error;
  }
}
